import random
def guess_the_number():
    number_to_guess=random.randint(1,100)
    attempts=0
    print("Welcome to guess the number")
    print("I am guessing a number from 1 to 100.Can you guess it.")
    while True:
        try:
            guess=int(input("Enter the number you are guessing"))
            attempts+=1
            if guess>number_to_guess:
                print("Too high")
            elif guess<number_to_guess:
                print("Too low") 
            else:
                print(f"Congratulations you have guessed the numbers in {attempts} attempts!")
        except ValueError:
            print("Enter a valid number")
if __name__=="__main__":
    guess_the_number()                          