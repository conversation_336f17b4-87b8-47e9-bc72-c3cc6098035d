import gym
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import random
from collections import deque

# ---------- Hyper-parameters ----------
ENV_NAME          = "CartPole-v1"
BATCH_SIZE        = 64
GAMMA             = 0.99
EPS_START, EPS_END, EPS_DECAY = 1.0, 0.01, 500
TARGET_UPDATE     = 10        # sync target net every N episodes
MEMORY_CAPACITY   = 10_000
LR                = 1e-3
NUM_EPISODES      = 500
# --------------------------------------

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# ---------- Neural Network ----------
class DQN(nn.Module):
    def _init_(self, obs_size, n_actions):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(obs_size, 128),
            nn.ReLU(),
            nn.Linear(128, 128),
            nn.ReLU(),
            nn.Linear(128, n_actions)
        )

    def forward(self, x):
        return self.net(x)

# ---------- Replay Buffer ----------
class ReplayBuffer:
    def _init_(self, capacity):
        self.buffer = deque(maxlen=capacity)

    def push(self, *transition):
        self.buffer.append(tuple(map(np.array, transition)))

    def sample(self, batch_size):
        batch = random.sample(self.buffer, batch_size)
        return map(torch.tensor, zip(*batch))

    def _len_(self):
        return len(self.buffer)

# ---------- Epsilon-Greedy ----------
def select_action(state, policy_net, eps):
    if random.random() > eps:
        with torch.no_grad():
            return policy_net(state).argmax().item()
    else:
        return random.randint(0, n_actions - 1)

# ---------- Environment ----------
env = gym.make(ENV_NAME)
obs_size  = env.observation_space.shape[0]
n_actions = env.action_space.n

policy_net = DQN(obs_size, n_actions).to(device)
target_net = DQN(obs_size, n_actions).to(device)
target_net.load_state_dict(policy_net.state_dict())
target_net.eval()

optimizer = optim.Adam(policy_net.parameters(), lr=LR)
memory    = ReplayBuffer(MEMORY_CAPACITY)

# ---------- Training Loop ----------
def optimize_model():
    if len(memory) < BATCH_SIZE:
        return
    states, actions, rewards, next_states, dones = memory.sample(BATCH_SIZE)
    states      = states.float().to(device)
    actions     = actions.long().to(device).unsqueeze(1)
    rewards     = rewards.float().to(device).unsqueeze(1)
    next_states = next_states.float().to(device)
    dones       = dones.bool().to(device).unsqueeze(1)

    q_values = policy_net(states).gather(1, actions)
    next_q_values = target_net(next_states).max(1)[0].detach().unsqueeze(1)
    expected_q = rewards + GAMMA * next_q_values * (~dones)

    loss = nn.MSELoss()(q_values, expected_q)
    optimizer.zero_grad()
    loss.backward()
    optimizer.step()

steps_done = 0
ep_scores  = []

for episode in range(NUM_EPISODES):
    state = env.reset()
    if isinstance(state, tuple): state = state[0]   # gym 0.26+
    state = torch.tensor(state, device=device, dtype=torch.float32).unsqueeze(0)
    score = 0

    while True:
        eps = EPS_END + (EPS_START - EPS_END) * np.exp(-steps_done / EPS_DECAY)
        action = select_action(state, policy_net, eps)
        result = env.step(action)
        next_state, reward, done, truncated = result[:4]
        done = done or truncated
        score += reward

        next_state = torch.tensor(next_state, device=device, dtype=torch.float32).unsqueeze(0)
        memory.push(state.cpu().squeeze().numpy(), action, reward, next_state.cpu().squeeze().numpy(), done)
        state = next_state
        optimize_model()
        steps_done += 1

        if done:
            ep_scores.append(score)
            if episode % 20 == 0:
                print(f"Episode {episode:4d}  |  Score: {score:.1f}  |  ε: {eps:.2f}")
            break

    # Update target network
    if episode % TARGET_UPDATE == 0:
        target_net.load_state_dict(policy_net.state_dict())

print(f"Training finished. Average score over last 50 episodes: {np.mean(ep_scores[-50:]):.1f}")